import { NextResponse } from "next/server";

// Simple in-memory database
let events: any[] = [];

export async function GET() {
  return NextResponse.json(events);
}

export async function POST(request: Request) {
  const newEvent = await request.json();
  const eventWithId = { ...newEvent, id: Date.now().toString() };
  events.push(eventWithId);
  return NextResponse.json(eventWithId, { status: 201 });
}

export async function PUT(request: Request) {
  const { id, ...updatedEvent } = await request.json();
  const index = events.findIndex((e) => e.id === id);
  
  if (index !== -1) {
    events[index] = { ...events[index], ...updatedEvent };
    return NextResponse.json(events[index]);
  } else {
    return NextResponse.json(
      { message: "Event not found" },
      { status: 404 }
    );
  }
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get("id");
  
  if (!id) {
    return NextResponse.json(
      { message: "ID is required" },
      { status: 400 }
    );
  }
  
  events = events.filter((e) => e.id !== id);
  return new NextResponse(null, { status: 204 });
}