@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply h-full overflow-x-hidden dark:text-gray-300 text-gray-600 font-normal bg-[#FAFAFD];

    &.dark {
      @apply bg-[#0f172a] text-gray-300;
    }
  }
  html,
  body {
    @apply h-full;
  }

  .DashSpace-app-wrapper {
    @apply relative;
  }
  .single-menu-item .ReactCollapse--collapse,
  .accordion .ReactCollapse--collapse,
  .ReactCollapse--collapse {
    transition: height 400ms;
  }

  .space-xy > :not([hidden]) {
    @apply mr-[10px] mb-[10px];
  }
  .space-xy-6 {
    > div,
    > button,
    > a,
    label,
    > * {
      @apply mr-4 mb-2;
    }
  }

  html[dir="rtl"] {
    .recharts-wrapper {
      direction: rtl;
    }
    .recharts-yAxis {
      .recharts-text {
        text-anchor: start;
      }
    }
  }
  .DashSpace-app {
    .leaflet-control {
      z-index: 0 !important;
    }
    .leaflet-control-container {
      z-index: 555 !important;
      position: relative;
    }
    .recharts-curve.recharts-tooltip-cursor {
      display: none;
    }
    .recharts-wrapper.bar-chart {
      .recharts-tooltip-cursor {
        fill: transparent;
      }
    }
    .recharts-tooltip-wrapper {
      border: none !important;
    }
  }
}
@layer components {
  @import "components/_table.scss";
  @import "components/_map.scss";
  @import "components/_progress";
  @import "components/_tippy";
  @import "components/_swiper";
  @import "components/_alert";
  @import "components/_card";
  @import "components/_auth";
  @import "components/_timeline";
  @import "components/_button";
  @import "components/_badge";
  @import "components/_typography";
  @import "components/_form";
  @import "components/_input-group";
  @import "components/_react-select";
  @import "components/_pagination";
  @import "components/_breadcrumbs";
  @import "layout/header";
  @import "layout/footer";
  @import "layout/sidebar";
  @import "layout/settings";
  @import "utility/mix";
  @import "utility/loading";
  @import "utility/css-animation";
  @import "utility/calander";
  @import "utility/full-calender";
  @import "components/print.scss";
}
@layer utilities {
}
