"use client";

import { useState, useEffect, useRef, useMemo } from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin, { Draggable } from "@fullcalendar/interaction";
import listPlugin from "@fullcalendar/list";
import {
  useGetCategoriesQuery,
  useGetCalendarEventsQuery,
  useCreateCalendarEventMutation,
  useEditCalendarEventMutation,
  useDeleteCalendarEventMutation,
} from "@/store/calendarSlice";
import EventModal from "./event-dialog";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { Plus, Calendar, Filter } from "lucide-react";

import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";

const CalendarPage = () => {
  const calendarRef = useRef(null);

  const { data: getCategories } = useGetCategoriesQuery();
  const {
    data: getCalendarEvents,
    isLoading,
    isError,
    error,
  } = useGetCalendarEventsQuery();
  const [createCalendarEvent] = useCreateCalendarEventMutation();
  const [editCalendarEvent] = useEditCalendarEventMutation();
  const [deleteCalendarEvent] = useDeleteCalendarEventMutation();

  const [calendarEvents, setCalendarEvents] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<any>(null);
  const [editEvent, setEditEvent] = useState<any>(null);
  const [showModal, setShowModal] = useState(false);

  // dummy external draggable events with proper keys
  const draggableEvents = useMemo(() => [
    { title: "New Event Planning", id: "drag-1", tag: "business", color: "hsl(var(--primary))" },
    { title: "Meeting", id: "drag-2", tag: "meeting", color: "hsl(var(--chart-3))" },
    { title: "Generating Reports", id: "drag-3", tag: "holiday", color: "hsl(var(--destructive))" },
    { title: "Create New theme", id: "drag-4", tag: "etc", color: "hsl(var(--chart-2))" },
  ], []);

  useEffect(() => {
    setCalendarEvents(getCalendarEvents ?? []);
    setCategories(getCategories ?? []);
    setSelectedCategories(getCategories?.map((c: any) => c.value) ?? []);
  }, [getCalendarEvents, getCategories]);

  useEffect(() => {
    const draggableEl = document.getElementById("external-events");
    if (!draggableEl || isLoading) return;

    new Draggable(draggableEl, {
      itemSelector: ".fc-event",
      eventData: (eventEl) => {
        const title = eventEl.getAttribute("title") || "";
        const id = eventEl.getAttribute("data-id") || "";
        const event = draggableEvents.find((e) => e.id === id);
        const tag = event ? event.tag : "";
        return {
          title,
          id: `${Date.now()}-${Math.random()}`, // Ensure unique ID
          extendedProps: { calendar: tag },
        };
      },
    });
  }, [isLoading, draggableEvents]);

  // Date click
  const handleDateClick = (arg: any) => {
    setEditEvent(null);
    setSelectedEvent(arg);
    setShowModal(true);
  };

  // Event click
  const handleEventClick = (arg: any) => {
    setEditEvent(arg);
    setShowModal(true);
  };

  // Close modal
  const handleCloseModal = () => {
    setShowModal(false);
    setEditEvent(null);
    setSelectedEvent(null);
  };

  // Add event
  const handleAddEvent = (newEvent: any) => {
    createCalendarEvent(newEvent)
      .unwrap()
      .then(() => toast.success("Event added successfully"))
      .catch(() => toast.error("Failed to add event"));
  };

  // Edit event
  const handleEditEvent = (updatedEvent: any) => {
    editCalendarEvent({
      id: editEvent.event.id,
      event: updatedEvent,
    })
      .unwrap()
      .then(() => toast.info("Event updated successfully"))
      .catch(() => toast.error("Failed to update event"));
  };

  // Delete event
  const handleDeleteEvent = (id: string) => {
    deleteCalendarEvent(id)
      .unwrap()
      .then(() => toast.success("Event deleted successfully"))
      .catch(() => toast.error("Failed to delete event"));
  };

  // Category filter
  const handleCategorySelection = (category: string) => {
    if (selectedCategories.includes(category)) {
      setSelectedCategories(selectedCategories.filter((c) => c !== category));
    } else {
      setSelectedCategories([...selectedCategories, category]);
    }
  };

  // Category color mapping using shadcn colors
  const getCategoryColor = (category: string) => {
    switch (category) {
      case "holiday":
        return {
          bg: "#ff352e50",
          text: "var(--foreground)",
          border: "",
        };
      case "business":
        return {
          bg: "hsl(var(--primary))",
          text: "hsl(var(--primary-foreground))",
          border: "hsl(var(--primary))",
        };
      case "personal":
        return {
          bg: "hsl(var(--chart-2))",
          text: "hsl(var(--primary-foreground))",
          border: "hsl(var(--chart-2))",
        };
      case "family":
        return {
          bg: "hsl(var(--chart-1))",
          text: "hsl(var(--primary-foreground))",
          border: "hsl(var(--chart-1))",
        };
      case "meeting":
        return {
          bg: "hsl(var(--chart-3))",
          text: "hsl(var(--foreground))",
          border: "hsl(var(--chart-3))",
        };
      case "etc":
        return {
          bg: "hsl(var(--chart-4))",
          text: "hsl(var(--primary-foreground))",
          border: "hsl(var(--chart-4))",
        };
      default:
        return {
          bg: "hsl(var(--muted))",
          text: "hsl(var(--muted-foreground))",
          border: "hsl(var(--border))",
        };
    }
  };

  // event class with proper shadcn colors
  const handleClassName = (arg: any) => {
    const category = arg.event.extendedProps.calendar;
    const colors = getCategoryColor(category);
    return `!bg-[${colors.bg}] !text-[${colors.text}] !border-[${colors.border}] border-l-4`;
  };

  // Filter events
  const filteredEvents = calendarEvents?.filter((event) =>
    selectedCategories.includes(event.extendedProps.calendar)
  );

  if (isLoading) return <div>Loading calendar...</div>;
  if (isError)
    return (
      <div>
        Error:{" "}
        {typeof error === "object" && error !== null && "message" in error
          ? (error as any).message
          : JSON.stringify(error)}
      </div>
    );

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-4">
        {/* Sidebar */}
        <Card className="lg:col-span-3 col-span-1 order-2 lg:order-1">
          <CardHeader className="pb-3">
            <Button
              className="w-full gap-2"
              onClick={() => setShowModal(true)}
            >
              <Plus className="h-4 w-4" />
              Add Event
            </Button>
          </CardHeader>

          <CardContent className="space-y-6">
            <div id="external-events" className="space-y-3">
              <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                <Calendar className="h-4 w-4" />
                Drag and drop your event or click in the calendar
              </div>
              {draggableEvents.map((event) => {
                const colors = getCategoryColor(event.tag);
                return (
                  <div
                    key={event.id}
                    className={cn(
                      "fc-event cursor-pointer p-3 rounded-lg border transition-all hover:shadow-md",
                      "text-sm font-medium"
                    )}
                    style={{
                      backgroundColor: colors.bg,
                      color: colors.text,
                      borderColor: colors.border,
                    }}
                    title={event.title}
                    data-id={event.id}
                  >
                    <div className="flex items-center gap-2">
                      <div
                        className="w-2 h-2 rounded-full"
                        style={{ backgroundColor: colors.border }}
                      />
                      {event.title}
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm font-semibold uppercase text-muted-foreground">
                <Filter className="h-4 w-4" />
                Filter
              </div>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Checkbox
                    id="all"
                    checked={selectedCategories?.length === categories?.length}
                    onCheckedChange={() => {
                      if (selectedCategories?.length === categories?.length) {
                        setSelectedCategories([]);
                      } else {
                        setSelectedCategories(categories.map((c: any) => c.value));
                      }
                    }}
                  />
                  <Label htmlFor="all" className="font-medium">All</Label>
                </div>
                {categories?.map((category: any) => {
                  const colors = getCategoryColor(category.value);
                  return (
                    <div key={category.value} className="flex items-center gap-2">
                      <Checkbox
                        id={category.value}
                        checked={selectedCategories.includes(category.value)}
                        onCheckedChange={() =>
                          handleCategorySelection(category.value)
                        }
                      />
                      <div
                        className="w-3 h-3 rounded-full border"
                        style={{ backgroundColor: colors.bg, borderColor: colors.border }}
                      />
                      <Label htmlFor={category.value} className="font-medium">
                        {category.label}
                      </Label>
                      <Badge
                        variant="secondary"
                        className="ml-auto text-xs"
                      >
                        {filteredEvents?.filter(e => e.extendedProps.calendar === category.value).length || 0}
                      </Badge>
                    </div>
                  );
                })}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Calendar */}
        <Card className="lg:col-span-9 col-span-1 order-1 lg:order-2">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Calendar
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="calendar-wrapper">
              <FullCalendar
                plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin]}
                ref={calendarRef}
                headerToolbar={{
                  left: "prev,next today",
                  center: "title",
                  right: "dayGridMonth,timeGridWeek,timeGridDay,listWeek",
                }}
                events={filteredEvents}
                editable={true}
                selectable={true}
                droppable={true}
                eventClassNames={handleClassName}
                dateClick={handleDateClick}
                eventClick={handleEventClick}
                initialView="dayGridMonth"
                height="auto"
                aspectRatio={1.35}
                dayMaxEvents={3}
                moreLinkClick="popover"
                eventDisplay="block"
                displayEventTime={false}
                eventDidMount={(info) => {
                  const category = info.event.extendedProps.calendar;
                  const colors = getCategoryColor(category);
                  info.el.style.backgroundColor = colors.bg;
                  info.el.style.color = colors.text;
                  info.el.style.borderColor = colors.border;
                  info.el.style.borderLeftWidth = "4px";
                  info.el.style.borderRadius = "6px";
                  info.el.style.padding = "2px 6px";
                  info.el.style.fontSize = "12px";
                  info.el.style.fontWeight = "500";
                }}
                dayCellDidMount={(info) => {
                  info.el.classList.add("hover:bg-muted/50", "transition-colors");
                }}
                buttonText={{
                  today: "Today",
                  month: "Month",
                  week: "Week",
                  day: "Day",
                  list: "List"
                }}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Event Modal */}
      <EventModal
        showModal={showModal}
        onClose={handleCloseModal}
        categories={categories}
        onAdd={handleAddEvent}
        selectedEvent={selectedEvent}
        event={editEvent}
        onEdit={handleEditEvent}
        onDelete={handleDeleteEvent}
      />
    </div>
  );
};

export default CalendarPage;
