"use client";

import { useState, useEffect, useRef } from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin, { Draggable } from "@fullcalendar/interaction";
import listPlugin from "@fullcalendar/list";
import {
  useGetCategoriesQuery,
  useGetCalendarEventsQuery,
  useCreateCalendarEventMutation,
  useEditCalendarEventMutation,
  useDeleteCalendarEventMutation,
} from "@/store/calendarSlice";
import EventModal from "./event-dialog";
import { toast } from "sonner";

import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

const CalendarPage = () => {
  const calendarRef = useRef(null);

  const { data: getCategories } = useGetCategoriesQuery();
  const {
    data: getCalendarEvents,
    isLoading,
    isError,
    error,
  } = useGetCalendarEventsQuery();
  const [createCalendarEvent] = useCreateCalendarEventMutation();
  const [editCalendarEvent] = useEditCalendarEventMutation();
  const [deleteCalendarEvent] = useDeleteCalendarEventMutation();

  const [calendarEvents, setCalendarEvents] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<any>(null);
  const [editEvent, setEditEvent] = useState<any>(null);
  const [showModal, setShowModal] = useState(false);

  // dummy external draggable events
  const events = [
    { title: "New Event Planning", id: "1", tag: "business" },
    { title: "Meeting", id: "2", tag: "meeting" },
    { title: "Generating Reports", id: "3", tag: "holiday" },
    { title: "Create New theme", id: "4", tag: "etc" },
  ];

  useEffect(() => {
    setCalendarEvents(getCalendarEvents ?? []);
    setCategories(getCategories ?? []);
    setSelectedCategories(getCategories?.map((c: any) => c.value) ?? []);
  }, [getCalendarEvents, getCategories]);

  useEffect(() => {
    const draggableEl = document.getElementById("external-events");
    if (!draggableEl) return;

    new Draggable(draggableEl, {
      itemSelector: ".fc-event",
      eventData: (eventEl) => {
        const title = eventEl.getAttribute("title") || "";
        const id = eventEl.getAttribute("data") || "";
        const event = events.find((e) => e.id === id);
        const tag = event ? event.tag : "";
        return {
          title,
          id,
          extendedProps: { calendar: tag },
        };
      },
    });
  }, [isLoading]);

  // Date click
  const handleDateClick = (arg: any) => {
    setEditEvent(null);
    setSelectedEvent(arg);
    setShowModal(true);
  };

  // Event click
  const handleEventClick = (arg: any) => {
    setEditEvent(arg);
    setShowModal(true);
  };

  // Close modal
  const handleCloseModal = () => {
    setShowModal(false);
    setEditEvent(null);
    setSelectedEvent(null);
  };

  // Add event
  const handleAddEvent = (newEvent: any) => {
    createCalendarEvent(newEvent)
      .unwrap()
      .then(() => toast.success("Event added successfully"))
      .catch(() => toast.error("Failed to add event"));
  };

  // Edit event
  const handleEditEvent = (updatedEvent: any) => {
    editCalendarEvent({
      id: editEvent.event.id,
      event: updatedEvent,
    })
      .unwrap()
      .then(() => toast.info("Event updated successfully"))
      .catch(() => toast.error("Failed to update event"));
  };

  // Delete event
  const handleDeleteEvent = (id: string) => {
    deleteCalendarEvent(id)
      .unwrap()
      .then(() => toast.success("Event deleted successfully"))
      .catch(() => toast.error("Failed to delete event"));
  };

  // Category filter
  const handleCategorySelection = (category: string) => {
    if (selectedCategories.includes(category)) {
      setSelectedCategories(selectedCategories.filter((c) => c !== category));
    } else {
      setSelectedCategories([...selectedCategories, category]);
    }
  };

  // event class
  const handleClassName = (arg: any) => {
    switch (arg.event.extendedProps.calendar) {
      case "holiday":
        return "bg-red-500 text-white";
      case "business":
        return "bg-blue-500 text-white";
      case "personal":
        return "bg-green-500 text-white";
      case "family":
        return "bg-indigo-500 text-white";
      case "meeting":
        return "bg-yellow-500 text-black";
      default:
        return "bg-gray-500 text-white";
    }
  };

  // Filter events
  const filteredEvents = calendarEvents?.filter((event) =>
    selectedCategories.includes(event.extendedProps.calendar)
  );

  if (isLoading) return <div>Loading calendar...</div>;
  if (isError)
    return (
      <div>
        Error:{" "}
        {typeof error === "object" && error !== null && "message" in error
          ? (error as any).message
          : JSON.stringify(error)}
      </div>
    );

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-12 gap-4">
        {/* Sidebar */}
        <Card className="lg:col-span-3 col-span-12 p-4">
          <Button
            className="w-full"
            onClick={() => setShowModal(true)}
          >
            + Add Event
          </Button>

          <div id="external-events" className="space-y-2 mt-6">
            <p className="text-sm text-muted-foreground">
              Drag and drop your event or click in the calendar
            </p>
            {events.map((event) => (
              <div
                key={event.id}
                className="fc-event cursor-pointer p-2 rounded bg-muted"
                title={event.title}
                data-id={event.id}
              >
                {event.title}
              </div>
            ))}
          </div>

          <div className="mt-6 text-xs font-semibold uppercase text-muted-foreground">
            Filter
          </div>
          <ul className="space-y-2 mt-2">
            <li className="flex items-center gap-2">
              <Checkbox
                id="all"
                checked={selectedCategories?.length === categories?.length}
                onCheckedChange={() => {
                  if (selectedCategories?.length === categories?.length) {
                    setSelectedCategories([]);
                  } else {
                    setSelectedCategories(categories.map((c: any) => c.value));
                  }
                }}
              />
              <Label htmlFor="all">All</Label>
            </li>
            {categories?.map((category: any) => (
              <li key={category.value} className="flex items-center gap-2">
                <Checkbox
                  id={category.value}
                  checked={selectedCategories.includes(category.value)}
                  onCheckedChange={() =>
                    handleCategorySelection(category.value)
                  }
                />
                <Label htmlFor={category.value}>{category.label}</Label>
              </li>
            ))}
          </ul>
        </Card>

        {/* Calendar */}
        <Card className="lg:col-span-9 col-span-12 p-4">
          <FullCalendar
            plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin]}
            ref={calendarRef}
            headerToolbar={{
              left: "prev,next today",
              center: "title",
              right: "dayGridMonth,timeGridWeek,timeGridDay,listWeek",
            }}
            events={filteredEvents}
            editable={true}
            selectable={true}
            droppable={true}
            eventClassNames={handleClassName}
            dateClick={handleDateClick}
            eventClick={handleEventClick}
            initialView="dayGridMonth"
          />
        </Card>
      </div>

      {/* Event Modal */}
      <EventModal
        showModal={showModal}
        onClose={handleCloseModal}
        categories={categories}
        onAdd={handleAddEvent}
        selectedEvent={selectedEvent}
        event={editEvent}
        onEdit={handleEditEvent}
        onDelete={handleDeleteEvent}
      />
    </div>
  );
};

export default CalendarPage;
