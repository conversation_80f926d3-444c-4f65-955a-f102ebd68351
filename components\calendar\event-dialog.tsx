"use client";

import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";

import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";

const eventSchema = z.object({
  title: z.string().min(1, "Event name is required"),
  cata: z.string().min(1, "Category is required"),
});

type EventForm = z.infer<typeof eventSchema>;

interface EventModalProps {
  showModal: boolean;
  onClose: () => void;
  categories: string[];
  selectedEvent?: any;
  event?: any;
  onAdd: (event: any) => void;
  onEdit: (event: any) => void;
  onDelete: (id: string) => void;
}

export default function EventModal({
  showModal,
  onClose,
  categories,
  selectedEvent,
  event,
  onAdd,
  onEdit,
  onDelete,
}: EventModalProps) {
  const [startDate, setStartDate] = useState<Date | undefined>(new Date());
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<EventForm>({
    resolver: zodResolver(eventSchema),
  });

  useEffect(() => {
    if (event) {
      setStartDate(event.event.start);
      setEndDate(event.event.end);
      reset({
        title: event.event.title,
        cata: event.event.extendedProps?.calendar || "",
      });
    } else {
      reset({ title: "", cata: "" });
      setStartDate(new Date());
      setEndDate(new Date());
    }
  }, [event, reset]);

  const onSubmit = (data: EventForm) => {
    const updatedEvent = {
      ...event,
      title: data.title,
      start: startDate,
      end: endDate,
      allDay: false,
      extendedProps: {
        calendar: data.cata,
      },
    };

    if (event) {
      onEdit(updatedEvent);
      toast.info("Event updated successfully");
    } else {
      onAdd(updatedEvent);
      toast.success("Event added successfully");
    }

    onClose();
    reset();
  };

  const handleDelete = () => {
    if (event?.event?.id) {
      onDelete(event.event.id);
      toast.success("Event deleted successfully");
    }
    setShowDeleteDialog(false);
    onClose();
  };

  return (
    <>
      <Dialog open={showModal} onOpenChange={onClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{event ? "Edit Event" : "Add Event"}</DialogTitle>
          </DialogHeader>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <Input
                placeholder="Enter Event Name"
                {...register("title")}
                defaultValue={event ? event.event.title : ""}
              />
              {errors.title && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.title.message}
                </p>
              )}
            </div>

            <div>
              <label className="block mb-1 text-sm font-medium">
                Start Date
              </label>
              <Calendar
                mode="single"
                selected={startDate}
                onSelect={setStartDate}
              />
            </div>

            <div>
              <label className="block mb-1 text-sm font-medium">End Date</label>
              <Calendar
                mode="single"
                selected={endDate}
                onSelect={setEndDate}
              />
            </div>

            <div>
              <Select
                onValueChange={(val) => setValue("cata", val)}
                defaultValue={event ? event.event.extendedProps?.calendar : ""}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((cat) => (
                    <SelectItem key={cat} value={cat}>
                      {cat}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.cata && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.cata.message}
                </p>
              )}
            </div>

            <DialogFooter>
              {event && (
                <Button
                  type="button"
                  variant="destructive"
                  onClick={() => setShowDeleteDialog(true)}
                >
                  Delete
                </Button>
              )}
              <Button type="submit">
                {event ? "Update" : "Add"} Event
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete confirmation */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Are you sure you want to delete this event?
            </AlertDialogTitle>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
