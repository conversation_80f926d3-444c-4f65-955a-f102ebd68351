.table-th {
  @apply text-gray-600 dark:text-gray-300 text-xs  whitespace-nowrap font-semibold  uppercase py-3 px-4 sm:px-5 ltr:text-left rtl:text-right;
}

.table-td {
  @apply text-gray-600 dark:text-gray-300 text-sm font-normal capitalize px-4 sm:px-5 py-3   border-gray-100 dark:border-gray-700 whitespace-nowrap;
}
.table-checkbox {
  @apply relative before:flex before:flex-col before:items-center before:justify-center   
  before:w-[18px] before:h-[18px] before:m-[-0.7px] before:bg-gray-50  before:border before:border-gray-10
  hover:before:border-indigo-500 
   before:absolute
    before:inset-0 before:rounded w-4 h-4 rounded checked:before:bg-indigo-500 
    checked:before:content-[url("https://api.iconify.design/heroicons-outline/check.svg?color=white")] 
    checked:before:leading-[10px];
}

// .table-checkbox {
//   @apply relative before:flex before:flex-col before:items-center before:justify-center
//   before:w-[18px] before:h-[18px] before:m-[-0.7px] before:bg-gray-100 dark:before:bg-gray-500  before:absolute
//     before:inset-0 before:rounded w-4 h-4 rounded checked:before:bg-gray-900
//     checked:before:content-[url("https://api.iconify.design/heroicons-outline/check.svg?color=white")] checked:before:leading-[10px]
//     checked:before:ring-2 checked:before:ring-gray-500
//     checked:before:ring-offset-2 checked:before:dark:ring-gray-700
//     checked:before:dark:ring-offset-0;
// }

.table-checkbox[type="checkbox"]:indeterminate {
  @apply before:items-center before:justify-center before:rounded  before:bg-indigo-500 before:border-indigo-500 before:leading-[10px]
    
  before:content-[url("https://api.iconify.design/heroicons/minus.svg?color=white")];
}
