// InValid Select
.DashSpace-app {
  .react-select.is-invalid {
    .select__control {
      border-color: none !important;
    }
  }
}

// Select Control(Input)
.DashSpace-app {
  .select__control {
    @apply border-gray-10;
  }
  .is-error {
    .select__control {
      @apply border-red-500;
    }
  }
  .select__control,
  .react-select__control {
    .select__input {
      @apply text-gray-500;
    }
    &.select__control--is-disabled {
      @apply cursor-not-allowed;
      .select__indicator-separator {
        @apply bg-gray-50 text-gray-800 placeholder:text-opacity-60;
      }
    }

    &.select__control--is-focused,
    &.react-select__control--is-focused {
      box-shadow: none;
      @apply border-indigo-600;
    }

    .select__indicator svg {
      cursor: pointer;
      @apply text-gray-600;
    }

    .select__indicator-separator {
      display: none;
    }
    .select__single-value {
      @apply text-gray-600   text-sm;
    }

    .select__placeholder {
      @apply text-gray-400;
    }
  }
  .is-error {
    .select__control,
    .react-select__control {
      .select__indicator svg {
        @apply text-red-600;
      }
    }
  }
}

// Select Menu
.DashSpace-app {
  .select__menu,
  .react-select__menu {
    .select__menu-list,
    .react-select__menu-list {
      .select__option,
      .react-select__option {
        cursor: pointer;

        &.select__option--is-focused {
          @apply bg-indigo-500 text-white;
        }

        &.select__option--is-selected {
          @apply bg-indigo-100 text-indigo-500;
        }
      }
    }

    .select__menu-list,
    .react-select__menu-list {
      .select__group {
        .select__group-heading {
          margin-bottom: 0.5rem;
          @apply text-gray-900 capitalize;
          font-weight: bolder;
          font-size: inherit;
        }
      }
    }
  }
}

.DashSpace-app {
  .select__multi-value,
  .react-select__multi-value {
    @apply text-indigo-500 bg-indigo-100;
    border-radius: 3px;
    margin: 0 0.7rem 0 0;

    .select__multi-value__label {
      @apply text-indigo-500;
      font-size: 0.85rem;
      border-radius: 10px;
      padding: 0.26rem 0.6rem;
    }

    .select__multi-value__remove {
      padding-left: 0;
      padding-right: 0.5rem;

      &:hover {
        background-color: inherit;
        color: inherit;
      }

      svg {
        height: 0.85rem;
        width: 0.85rem;
        &:hover {
          cursor: pointer;
        }
      }
    }
  }
}

// Select Borderless
.select-borderless {
  .select__control {
    border: 0;
    .select__indicators {
      display: none;
    }
  }
}

.dark {
  .select__control {
    @apply bg-indigo-500 border-gray-700;

    .select__indicator svg {
      @apply fill-gray-500;
    }

    .select__input {
      color: #cbd5e1 !important;
    }

    .select__indicator span,
    .select__single-value {
      color: #cbd5e1;
    }

    .select__multi-value {
      @apply bg-gray-700;
      .select__multi-value__label {
        @apply text-gray-300;
      }
    }
    .select__multi-value__remove {
      svg {
        fill: #cbd5e1;
      }
      &:hover {
        background-color: transparent !important;
      }
    }
    .select__placeholder {
      @apply text-gray-400;
    }
  }

  .select__menu {
    @apply bg-gray-800;
  }

  .select__menu,
  .react-select__menu {
    .select__menu-list,
    .react-select__menu-list {
      .select__option,
      .react-select__option {
        &.select__option--is-focused {
          @apply bg-gray-500 text-gray-300 bg-opacity-50;
        }

        &.select__option--is-selected {
          @apply bg-indigo-500 text-gray-200;
        }
      }
    }
  }
}

html[dir="rtl"] {
  .select__control {
    .select__loading-indicator {
      flex-direction: row-reverse;
    }
  }
}
