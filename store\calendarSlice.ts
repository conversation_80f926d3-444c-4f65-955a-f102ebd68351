import { apiSlice } from "./apiSlice";

export interface Category {
  label: string;
  value: string;
  className?: string;
  activeClass?: string;
}

export interface CalendarEvent {
  id: string;
  title: string;
  start: string | Date;
  end: string | Date;
  allDay?: boolean;
  extendedProps?: {
    calendar: string;
    [key: string]: any;
  };
}

interface EditEventPayload {
  id: string;
  event: Partial<CalendarEvent>;
}

export const calendarApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getCategories: builder.query<Category[], void>({
      query: () => "/categories",
    }),

    getCalendarEvents: builder.query<CalendarEvent[], void>({
      query: () => "/calendarEvents",
      providesTags: ["events"],
    }),

    createCalendarEvent: builder.mutation<CalendarEvent, CalendarEvent>({
      query: (event) => ({
        url: "/calendarEvents",
        method: "POST",
        body: event,
      }),
      invalidatesTags: ["events"],
    }),

    editCalendarEvent: builder.mutation<CalendarEvent, EditEventPayload>({
      query: ({ id, event }) => ({
        url: `/calendarEvents/${id}`,
        method: "PUT",
        body: { id, ...event },
      }),
      invalidatesTags: ["events"],
    }),

    deleteCalendarEvent: builder.mutation<{ success: boolean }, string>({
      query: (id) => ({
        url: `/calendarEvents/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["events"],
    }),
  }),
  overrideExisting: false
});

export const {
  useGetCategoriesQuery,
  useGetCalendarEventsQuery,
  useCreateCalendarEventMutation,
  useEditCalendarEventMutation,
  useDeleteCalendarEventMutation,
} = calendarApi;
