.form-label {
  @apply mb-2 text-gray-600 dark:text-gray-300 text-sm leading-6 capitalize  block w-full font-normal rtl:text-right rtl:block;
}

.text-control {
  @apply bg-white dark:bg-transparent  transition duration-300 ease-in-out border border-gray-10
   dark:border-gray-700 dark:text-gray-300 focus:border-indigo-600  dark:focus:border-indigo-600 
    focus:outline-none rounded
    placeholder:text-gray-400 text-gray-900 text-sm px-3  placeholder:font-normal dark:placeholder:text-gray-400 block w-full;
}

.input-help {
  @apply block text-gray-500 font-light leading-4 text-xs mt-2;
}
.textfiled-wrapper {
  @apply relative;
  &.is-error {
    .text-control {
      @apply border-red-500 focus:ring-red-500  focus:ring-1;
    }
  }
  &.is-valid {
    .text-control {
      @apply border-green-500 focus:ring-green-500 focus:ring-1;
    }
  }
}

.text-control[disabled] {
  @apply cursor-not-allowed bg-gray-100 bg-opacity-70 text-gray-800 placeholder:text-opacity-60 dark:bg-gray-600;
}

// file input
.file-control {
  @apply bg-transparent dark:bg-gray-900 dark:text-white transition duration-300 ease-in-out border border-gray-10 dark:border-gray-700 focus:ring-1 focus:ring-gray-900  focus:outline-none rounded   text-sm ltr:pl-3 rtl:pr-3   placeholder:font-normal;
}
.badge-title {
  @apply bg-gray-900 text-white px-2 py-[3px] rounded text-sm;
}

// datae picker
.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
  @apply bg-gray-900 border-gray-500;
}
