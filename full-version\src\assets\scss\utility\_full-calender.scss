.DashSpace-app {
  .fc-toolbar-chunk button {
    height: 50px;
    //min-width: 70px;
    &.fc-prev-button {
      &:after {
        // content: url("https://api.iconify.design/akar-icons/chevron-left.svg?color=white&width=24");
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
    &.fc-next-button {
      &:after {
        //content: url("https://api.iconify.design/akar-icons/chevron-right.svg?color=white&width=24");
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
  .fc-button {
    font-size: 14px !important;
    line-height: 14px !important;
    height: auto !important;
    text-transform: capitalize !important;
    font-family: Inter !important;
    padding: 12px 20px 12px 20px !important;
  }
  .fc .fc-button-primary {
    background: transparent !important;
    @apply text-indigo-500 dark:text-white border-indigo-200;
  }
  .fc .fc-button-primary:not(:disabled):active,
  .fc .fc-button-primary:not(:disabled).fc-button-active,
  .fc .fc-button-primary:hover {
    background: #4452ff !important;
    color: #fff !important;
  }
  .fc .fc-button-primary:not(:disabled).fc-button-active,
  .fc .fc-button-primary:not(:disabled):active {
    border-color: #4452ff !important;
  }
  .fc .fc-button-primary:disabled {
    background: #b6bff3 !important;
    border-color: #b6bff3 !important;
    color: #4452ff !important;
    @apply cursor-not-allowed;
  }

  .fc .fc-daygrid-day.fc-day-today {
    background: rgba(95, 99, 242, 0.04) !important;
  }

  .fc .fc-button-primary:focus {
    box-shadow: none !important;
  }
  .fc-theme-standard .fc-scrollgrid {
    border-color: #eef1f9 !important;
  }

  .fc-theme-standard td,
  .fc-theme-standard th {
    @apply border-gray-100 dark:border-gray-700;
  }
  .fc-col-header-cell .fc-scrollgrid-sync-inner {
    @apply bg-gray-50 dark:bg-gray-700  text-xs text-gray-500 dark:text-gray-300 font-normal py-3;
  }
  .fc-daygrid-day-top {
    @apply text-sm px-3 py-2  text-gray-900 dark:text-white font-normal;
  }
  .fc-h-event .fc-event-main-frame {
    @apply justify-center text-center w-max mx-auto;
    .fc-event-time {
      @apply font-normal flex-none;
    }
  }
  .fc-event-time {
    @apply text-sm font-normal;
  }
  .fc-event-title {
    font-size: 14px !important;
    font-weight: 300 !important;
  }
  .fc .fc-toolbar-title {
    @apply text-lg font-normal text-gray-600 dark:text-gray-300;
  }
  // event css
  .fc-daygrid-event-dot {
    @apply hidden;
  }

  @media (max-width: 981px) {
    .fc-button-group,
    .fc .fc-toolbar {
      display: block !important;
    }
    .fc .fc-toolbar {
      @apply space-y-4;
    }
    .fc-toolbar-chunk {
      @apply space-y-4;
    }
    .fc .fc-button {
      padding: 0.4em 0.65em !important;
    }
  }
  .fc .fc-timegrid-axis-cushion,
  .fc .fc-timegrid-slot-label-cushion {
    @apply dark:text-gray-300;
  }
  .fc .fc-list-event:hover td {
    @apply bg-inherit;
  }
  .fc .fc-list-event-dot {
    @apply hidden;
  }
  .fc-direction-ltr .fc-list-day-text,
  .fc-direction-rtl .fc-list-day-side-text,
  .fc-direction-ltr .fc-list-day-side-text,
  .fc-direction-rtl .fc-list-day-text {
    font-size: 16px;
    font-weight: 500;
  }
}

//

.dark {
  .fc-col-header-cell .fc-scrollgrid-sync-inner {
    @apply bg-gray-700 text-gray-300;
  }
  .fc-daygrid-day-top {
    @apply text-gray-300;
  }
  .fc .fc-day-other .fc-daygrid-day-top {
    @apply opacity-70;
  }
  .fc .fc-button-primary {
    @apply border-gray-600 text-gray-300;
  }
  .fc-theme-standard td,
  .fc-theme-standard th {
    @apply border-gray-700;
  }
  .fc .fc-toolbar-title {
    @apply text-gray-300;
  }
  .fc .fc-button-primary:not(:disabled):active,
  .fc .fc-button-primary:not(:disabled).fc-button-active,
  .fc .fc-button-primary:hover {
    background: #0f172a !important;
  }

  .fc .fc-button-primary:disabled {
    background: #334155 !important;
    border-color: #334155 !important;
  }

  .fc .fc-daygrid-day.fc-day-today {
    background: #334155 !important;
  }

  .fc-theme-standard .fc-scrollgrid {
    border-color: #334155 !important;
  }
}
.DashSpace-calender {
  .primary {
    @apply bg-indigo-500 border-none text-white text-center px-2 font-medium text-sm;
  }
  .secondary {
    @apply bg-fuchsia-500 border-none text-white text-center px-2 font-medium text-sm;
  }
  .danger {
    @apply bg-red-500 border-none text-white text-center px-2 font-medium text-sm;
  }
  .info {
    @apply bg-cyan-500 border-none text-white text-center px-2 font-medium text-sm;
  }
  .warning {
    @apply bg-yellow-500 border-none text-white text-center px-2 font-medium text-sm;
  }
  .success {
    @apply bg-green-500 border-none text-white text-center px-2 font-medium text-sm;
  }
  .dark {
    @apply bg-slate-900 border-none text-white text-center px-2 font-medium text-sm;
  }
}
