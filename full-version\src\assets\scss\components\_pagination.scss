.pagination {
  li {
    .prev-next-btn {
      @apply text-sm;
    }
    .prev-next-btn {
      &:disabled {
        @apply cursor-not-allowed opacity-50;
      }
    }
  }
  @apply flex items-center space-x-4 flex-wrap rtl:space-x-reverse;
  li {
    a,
    div,
    .page-link {
      @apply bg-gray-100 dark:bg-gray-700 dark:text-gray-400 text-gray-900 text-sm font-normal rounded flex h-6 w-6 items-center justify-center transition-all duration-150;
      &.active {
        @apply bg-indigo-500  text-white font-medium;
      }
    }
  }
  &.bordered {
    @apply border border-[#D8DEE6] rounded-[3px] py-1 px-2;
    li {
      @apply text-gray-500;
      &:first-child,
      &:last-child {
        button {
          @apply hover:bg-gray-900 hover:text-white transition duration-150 text-gray-500 h-6 w-6 flex items-center justify-center rounded;
        }
      }
      a,
      div,
      .page-link {
        @apply bg-transparent text-gray-500;
        &.active {
          @apply bg-gray-900 text-white;
        }
      }
    }
  }
  &.border-group {
    @apply border border-[#D8DEE6] rounded-[3px]  px-0 space-x-0 rtl:space-x-reverse;
    li {
      @apply border-r border-[#D8DEE5] h-full flex flex-col  justify-center px-3  last:border-none text-gray-500;
      a,
      div,
      .page-link {
        @apply bg-transparent text-gray-500 dark:text-white h-auto w-auto;
        &.active {
          @apply text-gray-900 dark:text-white text-lg;
        }
      }
    }
  }
}
