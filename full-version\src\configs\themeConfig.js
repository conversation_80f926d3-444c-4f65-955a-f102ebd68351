const themeConfig = {
  app: {
    name: "DashSpace React",
  },
  // layout
  layout: {
    isRTL: false,
    darkMode: false,
    semiDarkMode: false,
    contentWidth: "full",
    type: "vertical",
    menu: {
      isCollapsed: false,
      isHidden: false,
    },
    mobileMenu: false,
    customizer: false,
  },
  colors: {
    primary: "#3b82f6",
    secondary: "#d946ef",
    danger: "#ef4444",
    black: "#000",
    warning: "#eab308",
    info: "#06b6d4",
    light: "#425466",
    success: "#22c55e",
    chart_grid_light: "#E9EAF0",
    chart_grid_dark: "#374151",
    chart_text_light: "#4b5563",
    chart_text_dark: "#d1d5db",
  },
};

export default themeConfig;
