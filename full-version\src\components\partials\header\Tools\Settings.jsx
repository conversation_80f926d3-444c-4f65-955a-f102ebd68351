import React from "react";
import {
  Sheet,
  She<PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>ger,
} from "@/components/ui/sheet";
import Semidark from "./Semidark";
import RtlSwicth from "./Rtl";
import Theme from "./Theme";
import ContentWidth from "./ContentWidth";
import Menulayout from "./Menulayout";
import MenuClose from "./MenuClose";
import MenuHidden from "./MenuHidden";
import useWidth from "@/hooks/useWidth";
const Settings = () => {
  const { width, breakpoints } = useWidth();
  return (
    <Sheet>
      <SheetTrigger asChild>
        <div className="text-gray-600 dark:text-gray-100 text-2xl  cursor-pointer inline-block  ">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 256 256"
          >
            <path
              fill="currentColor"
              d="M128 80a48 48 0 1 0 48 48a48.05 48.05 0 0 0-48-48Zm0 80a32 32 0 1 1 32-32a32 32 0 0 1-32 32Zm88-29.84q.06-2.16 0-4.32l14.92-18.64a8 8 0 0 0 1.48-7.06a107.21 107.21 0 0 0-10.88-26.25a8 8 0 0 0-6-3.93l-23.72-2.64q-1.48-1.56-3-3L186 40.54a8 8 0 0 0-3.94-6a107.71 107.71 0 0 0-26.25-10.87a8 8 0 0 0-7.06 1.49L130.16 40h-4.32L107.2 25.11a8 8 0 0 0-7.06-1.48a107.6 107.6 0 0 0-26.25 10.88a8 8 0 0 0-3.93 6l-2.64 23.76q-1.56 1.49-3 3L40.54 70a8 8 0 0 0-6 3.94a107.71 107.71 0 0 0-10.87 26.25a8 8 0 0 0 1.49 7.06L40 125.84v4.32L25.11 148.8a8 8 0 0 0-1.48 7.06a107.21 107.21 0 0 0 10.88 26.25a8 8 0 0 0 6 3.93l23.72 2.64q1.49 1.56 3 3L70 215.46a8 8 0 0 0 3.94 6a107.71 107.71 0 0 0 26.25 10.87a8 8 0 0 0 7.06-1.49L125.84 216q2.16.06 4.32 0l18.64 14.92a8 8 0 0 0 7.06 1.48a107.21 107.21 0 0 0 26.25-10.88a8 8 0 0 0 3.93-6l2.64-23.72q1.56-1.48 3-3l23.78-2.8a8 8 0 0 0 6-3.94a107.71 107.71 0 0 0 10.87-26.25a8 8 0 0 0-1.49-7.06Zm-16.1-6.5a73.93 73.93 0 0 1 0 8.68a8 8 0 0 0 1.74 5.48l14.19 17.73a91.57 91.57 0 0 1-6.23 15l-22.6 2.56a8 8 0 0 0-5.1 2.64a74.11 74.11 0 0 1-6.14 6.14a8 8 0 0 0-2.64 5.1l-2.51 22.58a91.32 91.32 0 0 1-15 6.23l-17.74-14.19a8 8 0 0 0-5-1.75h-.48a73.93 73.93 0 0 1-8.68 0a8 8 0 0 0-5.48 1.74l-17.78 14.2a91.57 91.57 0 0 1-15-6.23L82.89 187a8 8 0 0 0-2.64-5.1a74.11 74.11 0 0 1-6.14-6.14a8 8 0 0 0-5.1-2.64l-22.58-2.52a91.32 91.32 0 0 1-6.23-15l14.19-17.74a8 8 0 0 0 1.74-5.48a73.93 73.93 0 0 1 0-8.68a8 8 0 0 0-1.74-5.48L40.2 100.45a91.57 91.57 0 0 1 6.23-15L69 82.89a8 8 0 0 0 5.1-2.64a74.11 74.11 0 0 1 6.14-6.14A8 8 0 0 0 82.89 69l2.51-22.57a91.32 91.32 0 0 1 15-6.23l17.74 14.19a8 8 0 0 0 5.48 1.74a73.93 73.93 0 0 1 8.68 0a8 8 0 0 0 5.48-1.74l17.77-14.19a91.57 91.57 0 0 1 15 6.23L173.11 69a8 8 0 0 0 2.64 5.1a74.11 74.11 0 0 1 6.14 6.14a8 8 0 0 0 5.1 2.64l22.58 2.51a91.32 91.32 0 0 1 6.23 15l-14.19 17.74a8 8 0 0 0-1.74 5.53Z"
            />
          </svg>
        </div>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader className=" space-y-1">
          <SheetTitle> Theme customizer</SheetTitle>
          <SheetDescription>Customize & Preview in Real Time</SheetDescription>
        </SheetHeader>
        <div className=" space-y-4 pt-6">
          {/* theme start */}
          <div>
            <Theme />
          </div>
          {/* semi dark start */}
          <div>
            <Semidark />
          </div>
          {/* hr start */}
          <div>
            <hr className="-mx-5 border-gray-200 dark:border-gray-700" />
          </div>
          {/* switch start */}

          <div>
            <RtlSwicth />
          </div>
          {/* hr start */}
          <div>
            <hr className="-mx-5 border-gray-200 dark:border-gray-700" />
          </div>
          {/* content width start */}
          <div>
            <ContentWidth />
          </div>
          {/* menu layout */}
          <div>{width >= breakpoints.xl && <Menulayout />}</div>
          {/* menu close */}
          <div className="pt-4">
            <MenuClose />
          </div>
          {/* menu hidden */}
          <div className="pt-2">
            <MenuHidden />
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default Settings;
