import React from "react";
import Card from "@/components/ui/Card";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./ReLineC<PERSON>";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./ReAreaC<PERSON>";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./ReBar<PERSON>hart";
import <PERSON>Scatter<PERSON><PERSON> from "./ScatterChart";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "./ReRadarChart";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./RePieChart";

const ChartJs = () => {
  return (
    <div className=" space-y-5">
      <Card title="Line Chart">
        <ReLineChart />
      </Card>
      <Card title="Area Chart">
        <ReAreaChart />
      </Card>
      <Card title="Bar Chart">
        <ReBarChart />
      </Card>
      <Card title="Scatter Chart">
        <ReScatterChart />
      </Card>
      <Card title="Radar Chart">
        <ReRadarChart />
      </Card>
      <Card title="Pie Chart">
        <RePieChart />
      </Card>
    </div>
  );
};

export default ChartJs;
