import React from "react";
import Dropdown from "@/components/ui/Dropdown";
import Icon from "@/components/ui/Icon";
import { Menu } from "@headlessui/react";
const actions = [
  {
    name: "Last 28 Days",
  },
  {
    name: " Last Month ",
  },
  {
    name: "  Last Year ",
  },
];
const SelectMonth = () => {
  return (
    <>
      <Dropdown
        classMenuItems=" w-[140px]"
        label={
          <span className="text-lg inline-flex h-6 w-6 flex-col items-center justify-center border border-gray-200 dark:border-gray-700 rounded dark:text-gray-400">
            <Icon icon="heroicons-outline:dots-horizontal" />
          </span>
        }
      >
        <div>
          {actions.map((item, i) => (
            <Menu.Item key={i}>
              <div
                className="`
                
                  hover:bg-gray-400 dark:hover:bg-gray-600 dark:bg-opacity-60 hover:text-white text-gray-900 dark:text-gray-300
                   w-full border-b border-b-gray-400 border-opacity-10 px-4 py-2 text-sm  last:mb-0 cursor-pointer first:rounded-t last:rounded-b flex  space-x-2 items-center `"
              >
                <span>{item.name}</span>
              </div>
            </Menu.Item>
          ))}
        </div>
      </Dropdown>
    </>
  );
};

export default SelectMonth;
