@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.65rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.623 0.214 259.815);
  --primary-foreground: oklch(0.97 0.014 254.604);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.623 0.214 259.815);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.623 0.214 259.815);
  --sidebar-primary-foreground: oklch(0.97 0.014 254.604);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.623 0.214 259.815);
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.546 0.245 262.881);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.488 0.243 264.376);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.546 0.245 262.881);
  --sidebar-primary-foreground: oklch(0.379 0.146 265.522);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.488 0.243 264.376);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Calendar Component Styles */
.calendar-wrapper {
  @apply w-full;
}

/* FullCalendar Theme Integration */
.fc {
  @apply text-foreground;
}

.fc-theme-standard .fc-scrollgrid {
  @apply border-border;
}

.fc-theme-standard td,
.fc-theme-standard th {
  @apply border-border;
}

.fc-theme-standard .fc-scrollgrid-section > * {
  @apply border-border;
}

/* Header Toolbar */
.fc-header-toolbar {
  @apply mb-4 flex-wrap gap-2;
}

.fc-toolbar-chunk {
  @apply flex items-center gap-2;
}

.fc-button-group {
  @apply flex rounded-md overflow-hidden border border-border;
}

.fc-button {
  @apply bg-background text-foreground border-border hover:bg-muted transition-colors px-3 py-2 text-sm font-medium;
}

.fc-button:not(:disabled):active,
.fc-button:not(:disabled).fc-button-active {
  @apply bg-primary text-primary-foreground border-primary;
}

.fc-button:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.fc-button:focus {
  @apply outline-none ring-2 ring-ring ring-offset-2;
}

/* Title */
.fc-toolbar-title {
  @apply text-xl font-semibold text-foreground;
}

/* Day Grid */
.fc-daygrid-day {
  @apply hover:bg-muted/50 transition-colors;
}

.fc-daygrid-day-number {
  @apply text-foreground p-2 text-sm font-medium;
}

.fc-day-today {
  @apply bg-primary/10;
}

.fc-day-today .fc-daygrid-day-number {
  @apply bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center;
}

/* Day Headers */
.fc-col-header-cell {
  @apply bg-muted/50 text-muted-foreground font-medium text-sm;
}

.fc-col-header-cell-cushion {
  @apply p-3;
}

/* Events */
.fc-event {
  @apply cursor-pointer transition-all hover:shadow-sm;
}

.fc-event-title {
  @apply font-medium text-xs;
}

.fc-daygrid-event {
  @apply rounded-md border-l-4 px-2 py-1 mb-1;
}

.fc-daygrid-event-harness {
  @apply mb-1;
}

/* More Link */
.fc-more-link {
  @apply text-primary hover:text-primary/80 text-xs font-medium;
}

/* Popover */
.fc-popover {
  @apply bg-popover text-popover-foreground border border-border rounded-lg shadow-lg;
}

.fc-popover-header {
  @apply bg-muted/50 px-3 py-2 border-b border-border font-medium text-sm;
}

.fc-popover-body {
  @apply p-2;
}

/* Time Grid */
.fc-timegrid-slot {
  @apply border-border;
}

.fc-timegrid-slot-label {
  @apply text-muted-foreground text-xs;
}

.fc-timegrid-axis {
  @apply text-muted-foreground;
}

/* List View */
.fc-list-table {
  @apply w-full;
}

.fc-list-day-cushion {
  @apply bg-muted/50 text-foreground font-medium p-3;
}

.fc-list-event {
  @apply hover:bg-muted/50 transition-colors;
}

.fc-list-event-title {
  @apply text-foreground;
}

.fc-list-event-time {
  @apply text-muted-foreground;
}

/* Responsive Design */
@media (max-width: 768px) {
  .fc-header-toolbar {
    @apply flex-col gap-3;
  }
  
  .fc-toolbar-chunk {
    @apply justify-center;
  }
  
  .fc-button {
    @apply px-2 py-1 text-xs;
  }
  
  .fc-toolbar-title {
    @apply text-lg;
  }
  
  .fc-col-header-cell-cushion {
    @apply p-2 text-xs;
  }
  
  .fc-daygrid-day-number {
    @apply p-1 text-xs;
  }
  
  .fc-daygrid-event {
    @apply text-xs px-1 py-0.5;
  }
}

@media (max-width: 640px) {
  .fc-header-toolbar .fc-toolbar-chunk:last-child {
    @apply order-first;
  }
  
  .fc-button-group {
    @apply flex-wrap;
  }
  
  .fc-button {
    @apply min-w-0 flex-1;
  }
  
  .fc-toolbar-title {
    @apply text-base;
  }
}

/* Dark mode adjustments */
.dark .fc-button {
  @apply bg-background text-foreground;
}

.dark .fc-button:not(:disabled):active,
.dark .fc-button:not(:disabled).fc-button-active {
  @apply bg-primary text-primary-foreground;
}

.dark .fc-day-today {
  @apply bg-primary/20;
}

/* Custom event colors will be applied via inline styles */
.fc-event.fc-event-draggable {
  @apply shadow-sm;
}

.fc-event:hover {
  @apply shadow-md scale-[1.02];
}

/* Scrollbar styling */
.fc-scroller::-webkit-scrollbar {
  @apply w-2;
}

.fc-scroller::-webkit-scrollbar-track {
  @apply bg-muted/50;
}

.fc-scroller::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

.fc-scroller::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}
