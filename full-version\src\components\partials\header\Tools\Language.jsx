import { Fragment, useState } from "react";
import { Listbox, Transition } from "@headlessui/react";

import Usa from "@/assets/images/flags/usa.png";
import Gn from "@/assets/images/flags/gn.png";
import Spa from "@/assets/images/flags/spa.png";
const months = [
  { name: "English", image: Usa },
  { name: "Germany", image: Gn },
  { name: "spanish", image: Spa },
];

const Language = () => {
  const [selected, setSelected] = useState(months[0]);

  return (
    <div className="md:block hidden">
      <Listbox value={selected} onChange={setSelected}>
        <div className="relative z-[22]">
          <Listbox.Button className="relative w-full flex items-center cursor-pointer ">
            <span className="inline-block lg:h-6 lg:w-6 h-5 w-5 rounded-full">
              <img
                src={selected.image}
                alt=""
                className="h-full w-full object-cover rounded-full "
              />
            </span>
          </Listbox.Button>
          <Transition
            as={Fragment}
            leave="transition ease-in duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Listbox.Options className="absolute min-w-[180px] ltr:right-0 rtl:left-0 md:top-[28px] top-[38px] w-auto max-h-60 overflow-auto border border-gray-200 dark:border-gray-700 rounded bg-white dark:bg-gray-800 mt-1 ">
              {months.map((item, i) => (
                <Listbox.Option key={i} value={item} as={Fragment}>
                  {({ active }) => (
                    <li
                      className={`
                      w-full border-b border-b-gray-400 border-opacity-10 px-2 py-2 last:border-none last:mb-0 cursor-pointer first:rounded-t last:rounded-b
                        ${
                          active
                            ? "bg-gray-100 dark:bg-gray-700 dark:bg-opacity-70 bg-opacity-50 dark:text-white "
                            : "text-gray-600 dark:text-gray-300"
                        }
                        `}
                    >
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <span className="flex-none">
                          <span className=" w-5 h-5 rounded-full inline-block">
                            <img
                              src={item.image}
                              alt=""
                              className="w-full h-full object-cover relative top-1 rounded-full"
                            />
                          </span>
                        </span>
                        <span className="flex-1  text-sm capitalize">
                          {item.name}
                        </span>
                      </div>
                    </li>
                  )}
                </Listbox.Option>
              ))}
            </Listbox.Options>
          </Transition>
        </div>
      </Listbox>
    </div>
  );
};

export default Language;
